package main

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/lib/pq"
)

func main() {
	fmt.Println("🚀 Simple PostgreSQL Connection Test")
	fmt.Println("====================================")

	// Database connection string (external server with password)
	connStr := "host=************** port=5432 user=blaeritipol password=YOUR_PASSWORD_HERE dbname=hvacdb sslmode=disable"

	// Open database connection
	db, err := sql.Open("postgres", connStr)
	if err != nil {
		log.Fatalf("Failed to open database: %v", err)
	}
	defer db.Close()

	// Test connection
	err = db.Ping()
	if err != nil {
		log.Fatalf("Failed to ping database: %v", err)
	}

	fmt.Println("✅ Successfully connected to PostgreSQL!")

	// Test simple query
	var version string
	err = db.QueryRow("SELECT version()").Scan(&version)
	if err != nil {
		log.Fatalf("Failed to query version: %v", err)
	}

	fmt.Printf("📊 PostgreSQL Version: %s\n", version)

	// Test creating a simple table
	_, err = db.Exec(`
		CREATE TABLE IF NOT EXISTS test_emails (
			id SERIAL PRIMARY KEY,
			subject VARCHAR(255),
			from_email VARCHAR(255),
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		log.Fatalf("Failed to create table: %v", err)
	}

	fmt.Println("✅ Test table created successfully!")

	// Insert test data
	_, err = db.Exec(`
		INSERT INTO test_emails (subject, from_email)
		VALUES ($1, $2)
	`, "Test HVAC Email", "<EMAIL>")
	if err != nil {
		log.Fatalf("Failed to insert data: %v", err)
	}

	fmt.Println("✅ Test data inserted successfully!")

	// Query test data
	rows, err := db.Query("SELECT id, subject, from_email, created_at FROM test_emails")
	if err != nil {
		log.Fatalf("Failed to query data: %v", err)
	}
	defer rows.Close()

	fmt.Println("📧 Test emails in database:")
	for rows.Next() {
		var id int
		var subject, fromEmail string
		var createdAt string

		err := rows.Scan(&id, &subject, &fromEmail, &createdAt)
		if err != nil {
			log.Fatalf("Failed to scan row: %v", err)
		}

		fmt.Printf("  ID: %d, Subject: %s, From: %s, Created: %s\n",
			id, subject, fromEmail, createdAt)
	}

	// Check for errors from iterating over rows
	if err = rows.Err(); err != nil {
		log.Fatalf("Error iterating rows: %v", err)
	}

	// Test count
	var count int
	err = db.QueryRow("SELECT COUNT(*) FROM test_emails").Scan(&count)
	if err != nil {
		log.Fatalf("Failed to count rows: %v", err)
	}

	fmt.Printf("📊 Total emails in test table: %d\n", count)

	// Clean up test table
	_, err = db.Exec("DROP TABLE test_emails")
	if err != nil {
		log.Printf("Warning: Failed to drop test table: %v", err)
	} else {
		fmt.Println("🧹 Test table cleaned up successfully!")
	}

	fmt.Println("\n🎉 Simple Database Test Completed Successfully!")
	fmt.Println("✅ PostgreSQL connection is working correctly")
	fmt.Println("✅ Basic SQL operations are functional")
	fmt.Println("✅ Ready for GORM integration!")
}
