package data

import (
	"github.com/go-kratos/kratos/v2/log"
	"gobackend-hvac-kratos/internal/conf"
	"gobackend-hvac-kratos/internal/email"
	"gobackend-hvac-kratos/internal/biz"
)

// NewBillionMailService creates a new BillionMail service
func NewBillionMailService(c *conf.Email, logger log.Logger) *email.BillionMailService {
	return email.NewBillionMailService(c, logger)
}

// NewDatabaseEmailStore creates a new database-backed email store
func NewDatabaseEmailStore(emailUsecase *biz.EmailUsecase, logger log.Logger) email.EmailStoreInterface {
	return email.NewDatabaseEmailStore(emailUsecase, logger)
}