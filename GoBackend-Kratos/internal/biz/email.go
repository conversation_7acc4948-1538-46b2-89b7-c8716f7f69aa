package biz

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/log"
)

// 📧 Email represents an email entity
type Email struct {
	ID          int64     `json:"id"`
	MessageID   string    `json:"message_id"`
	From        string    `json:"from"`
	To          []string  `json:"to"`
	CC          []string  `json:"cc,omitempty"`
	BCC         []string  `json:"bcc,omitempty"`
	Subject     string    `json:"subject"`
	Body        string    `json:"body"`
	HTMLBody    string    `json:"html_body,omitempty"`
	Headers     string    `json:"headers,omitempty"`
	Priority    string    `json:"priority"`
	Status      string    `json:"status"`
	SentAt      *time.Time `json:"sent_at,omitempty"`
	ReceivedAt  *time.Time `json:"received_at,omitempty"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// 🔍 EmailAnalysis represents email analysis results
type EmailAnalysis struct {
	ID               int64                  `json:"id"`
	EmailID          int64                  `json:"email_id"`
	SentimentScore   *float64               `json:"sentiment_score,omitempty"`
	UrgencyLevel     string                 `json:"urgency_level"`
	DetectedIntent   string                 `json:"detected_intent"`
	DetectedEntities map[string]interface{} `json:"detected_entities,omitempty"`
	KeyPhrases       []string               `json:"key_phrases,omitempty"`
	LanguageCode     string                 `json:"language_code"`
	IsSpam           bool                   `json:"is_spam"`
	ConfidenceScore  *float64               `json:"confidence_score,omitempty"`
	ProcessingTime   *int                   `json:"processing_time,omitempty"`
	HVACRelevance    *float64               `json:"hvac_relevance,omitempty"`
	Category         string                 `json:"category"`
	Priority         string                 `json:"priority"`
	ActionItems      []string               `json:"action_items,omitempty"`
	CreatedAt        time.Time              `json:"created_at"`
	UpdatedAt        time.Time              `json:"updated_at"`
}

// 📎 EmailAttachment represents email attachment analysis
type EmailAttachment struct {
	ID           int64     `json:"id"`
	EmailID      int64     `json:"email_id"`
	Filename     string    `json:"filename"`
	ContentType  string    `json:"content_type"`
	Size         int64     `json:"size"`
	TextContent  string    `json:"text_content,omitempty"`
	IsProcessed  bool      `json:"is_processed"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// 📊 EmailAnalysisResult represents complete analysis result
type EmailAnalysisResult struct {
	Email       *Email             `json:"email"`
	Analysis    *EmailAnalysis     `json:"analysis"`
	Attachments []*EmailAttachment `json:"attachments,omitempty"`
}

// 🔍 EmailSearchRequest represents search criteria
type EmailSearchRequest struct {
	Query         string     `json:"query,omitempty"`
	StartDate     *time.Time `json:"start_date,omitempty"`
	EndDate       *time.Time `json:"end_date,omitempty"`
	Category      string     `json:"category,omitempty"`
	Sentiment     string     `json:"sentiment,omitempty"`
	Priority      string     `json:"priority,omitempty"`
	HasHVAC       *bool      `json:"has_hvac,omitempty"`
	UrgencyLevel  string     `json:"urgency_level,omitempty"`
	IsSpam        *bool      `json:"is_spam,omitempty"`
	Limit         int        `json:"limit,omitempty"`
	Offset        int        `json:"offset,omitempty"`
}

// 📊 EmailSearchResponse represents search results
type EmailSearchResponse struct {
	Results    []*EmailAnalysisResult `json:"results"`
	Total      int64                  `json:"total"`
	Page       int                    `json:"page"`
	PageSize   int                    `json:"page_size"`
	TotalPages int                    `json:"total_pages"`
}

// 📊 DashboardStats represents email dashboard statistics
type DashboardStats struct {
	TotalEmails        int                    `json:"total_emails"`
	TodayEmails        int                    `json:"today_emails"`
	WeekEmails         int                    `json:"week_emails"`
	SentimentBreakdown *SentimentBreakdown    `json:"sentiment_breakdown"`
	CategoryBreakdown  *CategoryBreakdown     `json:"category_breakdown"`
	ProcessingMetrics  *ProcessingMetrics     `json:"processing_metrics"`
	TopKeywords        []KeywordCount         `json:"top_keywords"`
	HVACRelevantCount  int                    `json:"hvac_relevant_count"`
	SpamCount          int                    `json:"spam_count"`
}

type SentimentBreakdown struct {
	Positive int `json:"positive"`
	Neutral  int `json:"neutral"`
	Negative int `json:"negative"`
}

type CategoryBreakdown struct {
	Service   int `json:"service"`
	Sales     int `json:"sales"`
	Support   int `json:"support"`
	Emergency int `json:"emergency"`
	Other     int `json:"other"`
}

type ProcessingMetrics struct {
	AverageProcessingTime float64 `json:"average_processing_time"`
	TotalProcessingTime   int64   `json:"total_processing_time"`
	SuccessRate          float64 `json:"success_rate"`
}

type KeywordCount struct {
	Keyword string `json:"keyword"`
	Count   int    `json:"count"`
}

// EmailRepo defines the interface for email data operations
type EmailRepo interface {
	// Email CRUD operations
	CreateEmail(ctx context.Context, email *Email) (*Email, error)
	GetEmail(ctx context.Context, id int64) (*Email, error)
	GetEmailByMessageID(ctx context.Context, messageID string) (*Email, error)
	ListEmails(ctx context.Context, page, pageSize int32) ([]*Email, int64, error)
	UpdateEmail(ctx context.Context, email *Email) (*Email, error)
	DeleteEmail(ctx context.Context, id int64) error

	// Email Analysis operations
	CreateEmailAnalysis(ctx context.Context, analysis *EmailAnalysis) (*EmailAnalysis, error)
	GetEmailAnalysis(ctx context.Context, emailID int64) (*EmailAnalysis, error)
	UpdateEmailAnalysis(ctx context.Context, analysis *EmailAnalysis) (*EmailAnalysis, error)
	DeleteEmailAnalysis(ctx context.Context, emailID int64) error

	// Email Attachment operations
	CreateEmailAttachment(ctx context.Context, attachment *EmailAttachment) (*EmailAttachment, error)
	GetEmailAttachments(ctx context.Context, emailID int64) ([]*EmailAttachment, error)
	UpdateEmailAttachment(ctx context.Context, attachment *EmailAttachment) (*EmailAttachment, error)
	DeleteEmailAttachment(ctx context.Context, id int64) error

	// Complex operations
	GetEmailAnalysisResult(ctx context.Context, emailID int64) (*EmailAnalysisResult, error)
	SearchEmails(ctx context.Context, req *EmailSearchRequest) (*EmailSearchResponse, error)
	GetDashboardStats(ctx context.Context) (*DashboardStats, error)
	
	// Bulk operations
	StoreEmailAnalysisResult(ctx context.Context, result *EmailAnalysisResult) error
}

// EmailUsecase encapsulates email business logic
type EmailUsecase struct {
	repo EmailRepo
	log  *log.Helper
}

// NewEmailUsecase creates a new email usecase
func NewEmailUsecase(repo EmailRepo, logger log.Logger) *EmailUsecase {
	return &EmailUsecase{
		repo: repo,
		log:  log.NewHelper(logger),
	}
}

// StoreEmailAnalysisResult stores complete email analysis result
func (uc *EmailUsecase) StoreEmailAnalysisResult(ctx context.Context, result *EmailAnalysisResult) error {
	uc.log.WithContext(ctx).Infof("Storing email analysis result: %s", result.Email.Subject)
	
	// Validate required fields
	if result.Email == nil {
		return ErrEmailRequired
	}
	if result.Email.Subject == "" {
		return ErrEmailSubjectRequired
	}
	if result.Email.From == "" {
		return ErrEmailFromRequired
	}
	
	// Set timestamps
	now := time.Now()
	result.Email.CreatedAt = now
	result.Email.UpdatedAt = now
	
	if result.Analysis != nil {
		result.Analysis.CreatedAt = now
		result.Analysis.UpdatedAt = now
	}
	
	for _, attachment := range result.Attachments {
		attachment.CreatedAt = now
		attachment.UpdatedAt = now
	}
	
	return uc.repo.StoreEmailAnalysisResult(ctx, result)
}

// GetEmailAnalysisResult retrieves complete email analysis result
func (uc *EmailUsecase) GetEmailAnalysisResult(ctx context.Context, emailID int64) (*EmailAnalysisResult, error) {
	uc.log.WithContext(ctx).Infof("Getting email analysis result: %d", emailID)
	
	if emailID <= 0 {
		return nil, ErrInvalidEmailID
	}
	
	return uc.repo.GetEmailAnalysisResult(ctx, emailID)
}

// SearchEmails searches emails with criteria
func (uc *EmailUsecase) SearchEmails(ctx context.Context, req *EmailSearchRequest) (*EmailSearchResponse, error) {
	uc.log.WithContext(ctx).Infof("Searching emails with criteria")
	
	// Validate and set defaults
	if req.Limit <= 0 || req.Limit > 100 {
		req.Limit = 50
	}
	if req.Offset < 0 {
		req.Offset = 0
	}
	
	return uc.repo.SearchEmails(ctx, req)
}

// GetDashboardStats retrieves dashboard statistics
func (uc *EmailUsecase) GetDashboardStats(ctx context.Context) (*DashboardStats, error) {
	uc.log.WithContext(ctx).Info("Getting dashboard statistics")
	
	return uc.repo.GetDashboardStats(ctx)
}
